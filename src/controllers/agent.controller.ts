import { Request, Response, NextFunction } from "express";
import { generateAgentResponse } from "../services/agent.service";
import { sendError, sendSuccess, extractFinalAnswer } from "../utils/response.utils";
import {
  getChatbotByInboxId,
} from "../services/postgres/chatbot.service";
import { checkTenantCredit, deductTenantCredit } from "../services/credit.service";
import { searchProducts } from "../services/weaviate/product.service";
import { getProductDetailsByIds } from "../services/supabase/product.service";
import { messageBufferService, BufferedMessage } from "../services/queue/message-buffer.service";
import dotenv from "dotenv";

dotenv.config();

/**
 * Tạo phản hồi từ agent với Message Batching Optimization
 */
export const generateResponse = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {

    // C<PERSON><PERSON> hình resourceId và threadId cho Mastra Memory
    // resourceId: ID người dùng duy nhất (Facebook source_id hoặc fallback)
    const resourceId = req.body?.conversation?.contact_inbox?.source_id?.toString() ||
      req.body?.sender?.id?.toString() ||
      `contact_${req.body?.conversation?.contact_inbox?.contact_id || 'unknown'}`;

    // threadId: ID cuộc trò chuyện duy nhất (kết hợp inbox_id và conversation_id)
    const threadId = `inbox_${req.body?.inbox?.id}_conv_${req.body?.conversation?.id}`;

    const message = req.body?.content,
      inboxId = req.body?.inbox?.id,
      accountId = req.body?.account?.id,
      conversationId = req.body?.conversation?.id,
      messageType = req.body?.message_type,
      messageStatus = req.body?.conversation?.status,
      messageAttachments = req.body?.attachments,
      contactName = req.body?.sender?.name;

    // Debug logging để kiểm tra tin nhắn hình ảnh + text
    const hasImages = Boolean(messageAttachments && messageAttachments.length > 0);
    const hasText = Boolean(message && message.trim());
    console.log(`📨 Tin nhắn nhận được:`, {
      conversationId,
      hasImages,
      hasText,
      imageCount: messageAttachments?.length || 0,
      textContent: message ? `"${message.substring(0, 50)}..."` : 'null',
      messageType,
      messageStatus
    });

    if (messageType !== "incoming") {
      return sendSuccess(res, "okay, message type");
    }

    if (messageStatus !== 'pending') {
      return sendSuccess(res, "okay, status");
    }

    // Nếu có inbox_id và tenant_id, kiểm tra thông tin chatbot
    let chatbotInfo = null;
    if (inboxId) {
      const chatbotResult = await getChatbotByInboxId({
        inbox_id: inboxId.toString()
      });

      if (!chatbotResult.success) {
        return sendError(res, chatbotResult.message, 400);
      }

      chatbotInfo = chatbotResult.data;
    }

    // Kiểm tra credit của tenant trước khi xử lý
    const tenantIdToUse = chatbotInfo?.channel?.tenant_id;
    if (!tenantIdToUse) {
      return sendSuccess(res, { success: false, message: "Không tìm thấy tenant_id" });
    }

    try {
      const creditCheck = await checkTenantCredit(tenantIdToUse);

      if (!creditCheck.success || !creditCheck.hasCredit) {
        // Không gửi thông báo cho người dùng, chỉ log và dừng xử lý
        return sendSuccess(res, { success: false, message: "Không đủ credit" });
      }
    } catch (creditError) {
      console.error('Lỗi khi kiểm tra credit:', creditError);
      return sendSuccess(res, { success: false, message: "Lỗi khi kiểm tra credit" });
    }

    // Tạo BufferedMessage object
    const bufferedMessage: BufferedMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: message || '',
      attachments: messageAttachments || [],
      timestamp: new Date(),
      messageType,
      resourceId,
      threadId,
      conversationId: conversationId.toString(),
      accountId: accountId.toString(),
      inboxId: inboxId.toString(),
      metadata: {
        messageStatus,
        contactName,
        originalRequest: {
          inboxId,
          accountId,
          conversationId,
        }
      }
    };

    // Thêm tin nhắn vào buffer và tạo delayed job
    const bufferResult = await messageBufferService.addMessageToBuffer(
      bufferedMessage,
      chatbotInfo,
      tenantIdToUse.toString(),
      chatbotInfo?.channel?.bot_id || ''
    );

    if (!bufferResult.success) {
      console.error('❌ Không thể thêm tin nhắn vào buffer');

      // Log trạng thái kết nối để debug
      const connectionStatus = await messageBufferService.getConnectionStatus();
      console.error('📊 Trạng thái MessageBufferService:', connectionStatus);

      // Fallback: xử lý ngay lập tức theo cách cũ
      return await processMessageImmediately(req, res, next);
    }

    // Trả về response thành công
    return sendSuccess(res, {
      success: true,
      message: bufferResult.immediate ? "Đang xử lý ngay lập tức" : "Tin nhắn đã được thêm vào hàng đợi",
      batching: {
        immediate: bufferResult.immediate,
        jobId: bufferResult.jobId,
        delayTime: (chatbotInfo as any)?.instruction?.delay_time || 10,
      }
    });

  } catch (error) {
    console.error('❌ Lỗi trong generateResponse:', error);
    return sendError(res, "Lỗi hệ thống", 500);
  }
};

/**
 * Fallback: xử lý tin nhắn ngay lập tức theo cách cũ (cho trường hợp emergency)
 */
const processMessageImmediately = async (
  req: Request,
  res: Response,
  _next: NextFunction
) => {
  try {

    // Code xử lý cũ (giữ nguyên logic hiện tại)
    const resourceId = req.body?.conversation?.contact_inbox?.source_id?.toString() ||
      req.body?.sender?.id?.toString() ||
      `contact_${req.body?.conversation?.contact_inbox?.contact_id || 'unknown'}`;

    const threadId = `inbox_${req.body?.inbox?.id}_conv_${req.body?.conversation?.id}`;

    const message = req.body?.content,
      inboxId = req.body?.inbox?.id,
      accountId = req.body?.account?.id,
      conversationId = req.body?.conversation?.id,
      messageAttachments = req.body?.attachments,
      contactName = req.body?.sender?.name;

    // Lấy thông tin chatbot
    const chatbotResult = await getChatbotByInboxId({
      inbox_id: inboxId.toString()
    });

    if (!chatbotResult.success) {
      return sendError(res, chatbotResult.message, 400);
    }

    const chatbotInfo = chatbotResult.data;
    const tenantIdToUse = chatbotInfo?.channel?.tenant_id;

    if (!tenantIdToUse) {
      return sendError(res, "Không tìm thấy tenant_id", 400);
    }

    // Lấy thông tin tài khoản Mooly từ Supabase
    let apiToken = process.env.MOOLY_BOT_API_KEY || "";

    // Bật trạng thái đang nhập
    fetch(
      `https://app.mooly.vn/api/v1/accounts/${accountId}/conversations/${conversationId}/toggle_typing_status`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          api_access_token: apiToken,
        },
        body: JSON.stringify({
          typing_status: "on",
          is_private: false
        }),
      }
    );

    // Kiểm tra nếu tin nhắn là hình ảnh (content là null và có attachments)
    let response;

    try {
      if (messageAttachments && messageAttachments.length > 0) {
        // Xử lý tin nhắn hình ảnh (giữ nguyên logic cũ)
        // ... (code xử lý hình ảnh như cũ)

        // Mảng lưu trữ tất cả các product_id tìm được
        const allProductIds: string[] = [];

        // Xử lý từng hình ảnh một
        for (const attachment of messageAttachments) {
          // Lấy URL hình ảnh từ attachment
          const imageUrl = attachment.data_url || attachment.thumb_url;

          if (imageUrl) {

            // Tìm kiếm sản phẩm bằng hình ảnh thông qua Weaviate
            const searchResult = await searchProducts(
              {
                query: "query", // Không cần query text khi tìm bằng hình ảnh
                image_url: imageUrl,
                tenant_id: tenantIdToUse,
                bot_id: chatbotInfo?.channel?.bot_id || "",
              },
              1 // Giới hạn 1 kết quả cho mỗi hình ảnh
            );

            if (searchResult.success && searchResult.data.objects && searchResult.data.objects.length > 0) {
              // Lấy product_id từ kết quả tìm kiếm và thêm vào mảng
              const productId = searchResult.data.objects[0]?.properties?.product_id;
              if (productId && !allProductIds.includes(productId)) {
                allProductIds.push(productId);
              }
            }
          }
        }

        if (allProductIds.length === 0) {
          // Không tìm thấy sản phẩm nào
          const userMessage = message || "Tôi đang tìm kiếm sản phẩm trong hình ảnh này";

          response = await generateAgentResponse(
            [
              {
                role: "user",
                content: userMessage,
              },
            ],
            threadId,
            resourceId,
            chatbotInfo?.channel?.bot_id,
            tenantIdToUse?.toString(),
            {
              channel_type: 'bot',
              instruction: chatbotInfo?.instruction?.instruction || null,
              type: chatbotInfo?.instruction?.type || 'sale_bot',
            },
            accountId,
            conversationId,
            contactName
          );
        } else {
          // Xử lý sản phẩm tìm thấy - lấy thông tin chi tiết

          try {
            const productResult = await getProductDetailsByIds({
              productIds: allProductIds.slice(0, 3), // Giới hạn tối đa 3 sản phẩm
              tenant_id: tenantIdToUse,
            });

            let userMessage = message || "Tôi đang quan tâm đến sản phẩm trong hình ảnh này";

            if (productResult.success && productResult.data && productResult.data.length > 0) {
              const productDetails = productResult.data;

              // Thêm thông tin sản phẩm vào tin nhắn
              userMessage += '\n\nThông tin sản phẩm tìm thấy:\n';
              productDetails.forEach((product: any, index: number) => {
                userMessage += `\n${index + 1}. **${product.name}**\n`;
                userMessage += `   - Giá: ${product.price ? new Intl.NumberFormat('vi-VN').format(product.price) + ' VNĐ' : 'Liên hệ'}\n`;
                if (product.sku) userMessage += `   - SKU: ${product.sku}\n`;
                if (product.description) userMessage += `   - Mô tả: ${product.description.substring(0, 200)}${product.description.length > 200 ? '...' : ''}\n`;
                if (product.images && product.images.length > 0) {
                  userMessage += `   - Hình ảnh: ${product.images.slice(0, 3).map((img: any) => img.url).join(', ')}\n`;
                }
              });
            }

            response = await generateAgentResponse(
              [
                {
                  role: 'user',
                  content: userMessage,
                },
              ],
              threadId,
              resourceId,
              chatbotInfo?.channel?.bot_id,
              tenantIdToUse?.toString(),
              {
                channel_type: 'bot',
                instruction: chatbotInfo?.instruction?.instruction || null,
                type: chatbotInfo?.instruction?.type || 'sale_bot',
              },
              accountId,
              conversationId,
              contactName
            );
          } catch (error) {
            console.error('❌ Lỗi khi lấy thông tin chi tiết sản phẩm (fallback):', error);

            // Fallback nếu không lấy được thông tin sản phẩm
            const userMessage = message || "Tôi đang quan tâm đến sản phẩm trong hình ảnh này";

            response = await generateAgentResponse(
              [
                {
                  role: 'user',
                  content: userMessage,
                },
              ],
              threadId,
              resourceId,
              chatbotInfo?.channel?.bot_id,
              tenantIdToUse?.toString(),
              {
                channel_type: 'bot',
                instruction: chatbotInfo?.instruction?.instruction || null,
                type: chatbotInfo?.instruction?.type || 'sale_bot',
              },
              accountId,
              conversationId,
              contactName
            );
          }
        }
      } else {
        // Tin nhắn text thông thường
        response = await generateAgentResponse(
          [
            {
              role: "user",
              content: message,
            },
          ],
          threadId,
          resourceId,
          chatbotInfo?.channel?.bot_id,
          tenantIdToUse?.toString(),
          {
            channel_type: 'bot',
            instruction: chatbotInfo?.instruction?.instruction || null,
            type: chatbotInfo?.instruction?.type || 'sale_bot',
          },
          accountId,
          conversationId,
          contactName
        );
      }

      // Trừ credit sau khi xử lý thành công
      try {
        await deductTenantCredit({
          tenant_id: tenantIdToUse,
          creditAmount: 1,
          referenceType: 'agent_response_immediate',
          description: 'Xử lý tin nhắn ngay lập tức (fallback)',
        });
      } catch (creditError) {
        console.error('Lỗi khi trừ credit (fallback):', creditError);
      }
      response?.text ?
        fetch(
          `https://app.mooly.vn/api/v1/accounts/${accountId}/conversations/${conversationId}/messages`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              api_access_token: apiToken,
            },
            body: JSON.stringify({
              content: extractFinalAnswer(response?.text || "Xin lỗi, tôi không thể trả lời tin nhắn này"),
              "private": false
            }),
          }
        ) : null

      // Tắt typing indicator
      fetch(
        `https://app.mooly.vn/api/v1/accounts/${accountId}/conversations/${conversationId}/toggle_typing_status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            api_access_token: apiToken,
          },
          body: JSON.stringify({
            typing_status: "off",
            is_private: false
          }),
        }
      );

      return sendSuccess(res, {
        success: true,
        message: "Xử lý ngay lập tức thành công",
        response: extractFinalAnswer(response?.text || ''),
        fallback: true,
      });

    } catch (error) {
      console.error('Lỗi khi xử lý tin nhắn ngay lập tức:', error);

      // Tắt typing indicator trong trường hợp lỗi
      fetch(
        `https://app.mooly.vn/api/v1/accounts/${accountId}/conversations/${conversationId}/toggle_typing_status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            api_access_token: apiToken,
          },
          body: JSON.stringify({
            typing_status: "off",
            is_private: false
          }),
        }
      );

      throw error;
    }

  } catch (error) {
    console.error('❌ Lỗi trong processMessageImmediately:', error);
    return sendError(res, "Lỗi hệ thống", 500);
  }
};
